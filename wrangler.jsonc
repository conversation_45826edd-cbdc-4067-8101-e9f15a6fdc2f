{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "anthropic-proxy",
  "compatibility_date": "2025-05-27",
  "pages_build_output_dir": "./dist",
  "compatibility_flags": ["nodejs_compat"],
  "env": {
    "production": {
      "vars": {
        "SHARED_SECRET": "my-shared-secret",
        "ANTHROPIC_API_KEY": "my-anthropic-api-key"
      }
    }
  }
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  //   }
  // ],
  // "r2_buckets": [
  //   {
  //     "binding": "MY_BUCKET",
  //     "bucket_name": "my-bucket"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "MY_DB",
  //     "database_name": "my-database",
  //     "database_id": ""
  //   }
  // ],
  // "ai": {
  //   "binding": "AI"
  // }
}
