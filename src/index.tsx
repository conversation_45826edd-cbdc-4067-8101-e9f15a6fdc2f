import Anthropic from "@anthropic-ai/sdk";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { sign, verify } from "hono/jwt";
import { renderer } from "./renderer";
import { prompt } from "./utils/prompt";

interface CleanCodeRequest {
  code: string;
  imageUrl: string;
}

interface CleanCodeResponse {
  cleanCode: string;
  suggestions?: string[];
}

interface TokenRequest {
  clientId: string;
}

type Bindings = {
  ANTHROPIC_API_KEY: string;
  JWT_SECRET: string;
};

const app = new Hono<{ Bindings: Bindings }>();

app.use(renderer);

app.use(
  "/api/clean-code",
  cors({
    origin: ["https://figma.com"],
    allowMethods: ["GET", "POST", "OPTIONS"],
  })
);

// Generate token endpoint
app.post("/api/auth/token", async (c) => {
  const { clientId } = await c.req.json<TokenRequest>();
  // Validate clientId against allowed list if needed

  // Create short-lived token (15 minutes)
  const token = await sign(
    { clientId, exp: Math.floor(Date.now() / 1000) + 15 * 60 },
    c.env.JWT_SECRET
  );
  return c.json({ token });
});

// Modify auth middleware to use JWT
app.use("/api/clean-code/*", async (c, next) => {
  const authHeader = c.req.header("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  const token = authHeader.split(" ")[1];
  try {
    await verify(token, c.env.JWT_SECRET);
    await next();
  } catch (e) {
    return c.json({ error: "Invalid token" }, 401);
  }
});

app.post("/api/clean-code", async (c) => {
  const { code, imageUrl } = await c.req.json<CleanCodeRequest>();
  const apiKey = c.env.ANTHROPIC_API_KEY;

  if (!apiKey) {
    const res: CleanCodeResponse = {
      cleanCode: "No Anthropic API Key",
      suggestions: ["Error: Anthropic API key not configured on the server."],
    };
    return c.json(res, 500);
  }

  try {
    const client = new Anthropic({ apiKey });
    const base64Image = imageUrl.split(",")[1] || "";

    const res = await client.messages.create({
      model: "claude-3-5-sonnet-latest",
      max_tokens: 4000,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `${prompt}\nClean and optimize this code:\n${code}`,
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: "image/png",
                data: base64Image,
              },
            },
          ],
        },
      ],
    });

    const textPart = res.content.find((c: any) => c.type === "text");
    const cleanCode = textPart?.text || "";

    const response: CleanCodeResponse = {
      cleanCode,
      suggestions: [],
    };
    return c.json(response, res.status || 200);
  } catch (error) {
    const resp: CleanCodeResponse = {
      cleanCode: code,
      suggestions: [error instanceof Error ? error.message : "Unknown error"],
    };
    return c.json(resp, 500);
  }
});

app.get("/", (c) => {
  return c.render(<h1>Hello!</h1>);
});

export default app;
