# Anthropic Proxy

## Setup

### 1. Install dependencies

```bash
pnpm install
```

### 2. Environment Variables

This project uses environment variables for configuration. Set them up as follows:

#### For Local Development

Create a `.dev.vars` file in the root directory:

```bash
cp .env.example .dev.vars
```

Then edit `.dev.vars` with your actual values:

```
ANTHROPIC_API_KEY=your-actual-anthropic-api-key
JWT_SECRET=your-actual-jwt-secret
```

#### For Production

Use Wrangler secrets for production deployment:

```bash
# Set production secrets
wrangler secret put ANTHROPIC_API_KEY
wrangler secret put JWT_SECRET
```

### 3. Development

```bash
pnpm run dev
```

### 4. Deployment

```bash
# Deploy to staging
pnpm run deploy

# Deploy to production
pnpm run deploy:prod
```

[For generating/synchronizing types based on your Worker configuration run](https://developers.cloudflare.com/workers/wrangler/commands/#types):

```txt
npm run cf-typegen
```

Pass the `CloudflareBindings` as generics when instantiation `Hono`:

```ts
// src/index.ts
const app = new Hono<{ Bindings: CloudflareBindings }>();
```
